package com.glodon.qydata.service.system;

import com.glodon.qydata.common.AccountTypeService;
import com.glodon.qydata.common.enums.AccountTypeEnum;
import com.glodon.qydata.mapper.system.DataCleanupMapper;
import com.glodon.qydata.service.system.DataCleanupService.CleanupResult;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeCleanupResult;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeGroupStats;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeRecordIdInfo;
import com.glodon.qydata.service.system.DataCleanupService.QyCodeGroupStats;
import com.glodon.qydata.service.system.DataCleanupService.RecordIdInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DataCleanupService 测试类
 * 主要测试 zb_standards_build_standard 表组的数据清理功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
class DataCleanupServiceTest {

    @Mock
    private DataCleanupMapper dataCleanupMapper;

    @Mock
    private AccountTypeService accountTypeService;

    @InjectMocks
    private DataCleanupService dataCleanupService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testPerformBuildStandardDataCleanup_AnalysisOnly() {
        // 准备测试数据 - 三张表
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard")).thenReturn(2000L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail")).thenReturn(10000L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail_desc")).thenReturn(15000L);
        
        List<QyCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new QyCodeGroupStats("*********", 100L)); // 个人账号
        mockStats.add(new QyCodeGroupStats("ENTERPRISE009", 500L)); // 企业账号
        when(dataCleanupMapper.getBuildStandardQyCodeGroupStats("zb_standards_build_standard")).thenReturn(mockStats);

        List<RecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            mockRecordIds.add(new RecordIdInfo((long) i, "*********"));
        }
        for (int i = 101; i <= 600; i++) {
            mockRecordIds.add(new RecordIdInfo((long) i, "ENTERPRISE009"));
        }
        when(dataCleanupMapper.collectAllBuildStandardRecordIds("zb_standards_build_standard")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE009")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CleanupResult result = dataCleanupService.performBuildStandardDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(27000L, result.getTotalRecords()); // 2000 + 10000 + 15000
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_standards_build_standard 表组数据分析完成，未执行清理操作", result.getMessage());
        
        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getQyCode());
        assertEquals(100L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证分组统计
        assertNotNull(result.getQyCodeGroupStats());
        assertEquals(100L, result.getQyCodeGroupStats().get("*********"));
        assertEquals(500L, result.getQyCodeGroupStats().get("ENTERPRISE009"));

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteBuildStandardRecordsByQyCode(anyString(), anyString());
        verify(dataCleanupMapper, never()).deleteBuildStandardDetailRecordsByQyCode(anyString(), anyString());
    }

    @Test
    void testPerformBuildStandardDataCleanup_WithExecution() {
        // 准备测试数据 - 三张表
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard")).thenReturn(500L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail")).thenReturn(2500L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail_desc")).thenReturn(3000L);
        
        List<QyCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new QyCodeGroupStats("*********", 80L)); // 个人账号
        when(dataCleanupMapper.getBuildStandardQyCodeGroupStats("zb_standards_build_standard")).thenReturn(mockStats);

        List<RecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 80; i++) {
            mockRecordIds.add(new RecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllBuildStandardRecordIds("zb_standards_build_standard")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟新的删除操作流程
        List<Long> mockMainTableIds = List.of(1L, 2L, 3L); // 模拟主表ID列表
        when(dataCleanupMapper.collectBuildStandardMainTableIdsByQyCode("*********")).thenReturn(mockMainTableIds);
        when(dataCleanupMapper.deleteBuildStandardDetailRecordsByIds("zb_standards_build_standard_detail_desc", mockMainTableIds)).thenReturn(600);
        when(dataCleanupMapper.deleteBuildStandardDetailRecordsByIds("zb_standards_build_standard_detail", mockMainTableIds)).thenReturn(400);
        when(dataCleanupMapper.deleteBuildStandardMainRecordsByIds(mockMainTableIds)).thenReturn(80);

        // 执行测试（执行清理）
        CleanupResult result = dataCleanupService.performBuildStandardDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(1080L, result.getDeletedRecords()); // 80 + 400 + 600
        assertTrue(result.getMessage().contains("共删除 1080 条个人账号相关记录"));

        // 验证执行了新的删除操作流程（按正确顺序）
        verify(dataCleanupMapper, times(1)).collectBuildStandardMainTableIdsByQyCode("*********");
        verify(dataCleanupMapper, times(1)).deleteBuildStandardDetailRecordsByIds("zb_standards_build_standard_detail_desc", mockMainTableIds);
        verify(dataCleanupMapper, times(1)).deleteBuildStandardDetailRecordsByIds("zb_standards_build_standard_detail", mockMainTableIds);
        verify(dataCleanupMapper, times(1)).deleteBuildStandardMainRecordsByIds(mockMainTableIds);
    }

    @Test
    void testPerformBuildStandardDataCleanup_NoPersonalAccounts() {
        // 准备测试数据 - 只有企业账号
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard")).thenReturn(1000L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail")).thenReturn(5000L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail_desc")).thenReturn(8000L);
        
        List<QyCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new QyCodeGroupStats("ENTERPRISE011", 600L)); // 企业账号1
        mockStats.add(new QyCodeGroupStats("COMPANY003", 400L)); // 企业账号2
        when(dataCleanupMapper.getBuildStandardQyCodeGroupStats("zb_standards_build_standard")).thenReturn(mockStats);

        List<RecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 600; i++) {
            mockRecordIds.add(new RecordIdInfo((long) i, "ENTERPRISE011"));
        }
        for (int i = 601; i <= 1000; i++) {
            mockRecordIds.add(new RecordIdInfo((long) i, "COMPANY003"));
        }
        when(dataCleanupMapper.collectAllBuildStandardRecordIds("zb_standards_build_standard")).thenReturn(mockRecordIds);

        // 执行测试（执行清理）
        CleanupResult result = dataCleanupService.performBuildStandardDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(0L, result.getDeletedRecords()); // 没有个人账号，删除记录数为0
        
        // 验证个人账号数据列表为空
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(0, result.getPersonalAccountDataList().size());

        // 验证没有执行删除操作（因为没有个人账号）
        verify(dataCleanupMapper, never()).collectBuildStandardMainTableIdsByQyCode(anyString());
        verify(dataCleanupMapper, never()).deleteBuildStandardDetailRecordsByIds(anyString(), anyList());
        verify(dataCleanupMapper, never()).deleteBuildStandardMainRecordsByIds(anyList());
    }

    @Test
    void testPerformBuildStandardDataCleanup_EmptyMainTableIds() {
        // 准备测试数据 - 个人账号但主表无记录
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard")).thenReturn(100L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail")).thenReturn(500L);
        when(dataCleanupMapper.countTableRecords("zb_standards_build_standard_detail_desc")).thenReturn(800L);

        List<QyCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new QyCodeGroupStats("*********", 50L)); // 个人账号
        when(dataCleanupMapper.getBuildStandardQyCodeGroupStats("zb_standards_build_standard")).thenReturn(mockStats);

        List<RecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            mockRecordIds.add(new RecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllBuildStandardRecordIds("zb_standards_build_standard")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟主表ID收集返回空列表
        when(dataCleanupMapper.collectBuildStandardMainTableIdsByQyCode("*********")).thenReturn(new ArrayList<>());

        // 执行测试（执行清理）
        CleanupResult result = dataCleanupService.performBuildStandardDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(0L, result.getDeletedRecords()); // 主表无记录，删除记录数为0

        // 验证只执行了ID收集，没有执行删除操作
        verify(dataCleanupMapper, times(1)).collectBuildStandardMainTableIdsByQyCode("*********");
        verify(dataCleanupMapper, never()).deleteBuildStandardDetailRecordsByIds(anyString(), anyList());
        verify(dataCleanupMapper, never()).deleteBuildStandardMainRecordsByIds(anyList());
    }

    @Test
    void testPerformProjectInfoDataCleanup_AnalysisOnly() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_project_info")).thenReturn(8000L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 250L)); // 个人账号
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE012", 1200L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_project_info")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 250; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 251; i <= 1450; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE012"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_project_info")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE012")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performProjectInfoDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(8000L, result.getTotalRecords());
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_standards_project_info 表数据分析完成，未执行清理操作", result.getMessage());

        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(250L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证分组统计
        assertNotNull(result.getCustomerCodeGroupStats());
        assertEquals(250L, result.getCustomerCodeGroupStats().get("*********"));
        assertEquals(1200L, result.getCustomerCodeGroupStats().get("ENTERPRISE012"));

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformProjectInfoDataCleanup_WithExecution() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_project_info")).thenReturn(1000L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 180L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_project_info")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 180; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_project_info")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_project_info", "*********")).thenReturn(180);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performProjectInfoDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(180L, result.getDeletedRecords());
        assertTrue(result.getMessage().contains("共删除 180 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_project_info", "*********");
    }

    @Test
    void testPerformProjectInfoDataCleanup_MultiplePersonalAccounts() {
        // 准备测试数据 - 多个个人账号
        when(dataCleanupMapper.countTableRecords("zb_standards_project_info")).thenReturn(2000L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 120L)); // 个人账号1
        mockStats.add(new CustomerCodeGroupStats("*********", 80L));  // 个人账号2
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE013", 600L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_project_info")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 120; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 121; i <= 200; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 201; i <= 800; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE013"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_project_info")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE013")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_project_info", "*********")).thenReturn(120);
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_project_info", "*********")).thenReturn(80);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performProjectInfoDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(200L, result.getDeletedRecords()); // 120 + 80
        assertTrue(result.getMessage().contains("共删除 200 条个人账号相关记录"));

        // 验证个人账号数据列表
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(2, result.getPersonalAccountDataList().size());

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_project_info", "*********");
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_project_info", "*********");
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode("zb_standards_project_info", "ENTERPRISE013");
    }

    @Test
    void testPerformUnitDataCleanup_AnalysisOnly() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_unit")).thenReturn(5000L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 150L)); // 个人账号
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE014", 800L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_unit")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 150; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 151; i <= 950; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE014"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_unit")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE014")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performUnitDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(5000L, result.getTotalRecords());
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_standards_unit 表数据分析完成，未执行清理操作", result.getMessage());

        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(150L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证分组统计
        assertNotNull(result.getCustomerCodeGroupStats());
        assertEquals(150L, result.getCustomerCodeGroupStats().get("*********"));
        assertEquals(800L, result.getCustomerCodeGroupStats().get("ENTERPRISE014"));

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformUnitDataCleanup_WithExecution() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_unit")).thenReturn(800L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 120L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_unit")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 120; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_unit")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_unit", "*********")).thenReturn(120);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performUnitDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(120L, result.getDeletedRecords());
        assertTrue(result.getMessage().contains("共删除 120 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_unit", "*********");
    }

    @Test
    void testPerformUnitDataCleanup_EmptyTable() {
        // 准备测试数据 - 空表
        when(dataCleanupMapper.countTableRecords("zb_standards_unit")).thenReturn(0L);

        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_unit")).thenReturn(new ArrayList<>());
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_unit")).thenReturn(new ArrayList<>());

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performUnitDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertEquals(0L, result.getTotalRecords());
        assertTrue(result.isCleanupExecuted());
        assertEquals(0L, result.getDeletedRecords());

        // 验证个人账号数据列表为空
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(0, result.getPersonalAccountDataList().size());

        // 验证没有执行删除操作（因为没有数据）
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }
}
